import React, { useRef, useState, useEffect } from 'react'
import { Play } from 'lucide-react'
import { HeroContent } from '@/types'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

interface VideoShowcaseSectionProps {
  heroContent: HeroContent
}

const VideoShowcaseSection: React.FC<VideoShowcaseSectionProps> = ({
  heroContent
}) => {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const sectionRef = useRef<HTMLElement>(null)

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () =>
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  // Handle escape key for fullscreen exit
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        exitFullscreen()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isFullscreen])

  // Sync video playing state with actual video state
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handlePlay = () => setIsVideoPlaying(true)
    const handlePause = () => setIsVideoPlaying(false)

    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)

    return () => {
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
    }
  }, [])

  // GSAP ScrollTrigger animation for futuristic video expansion
  useEffect(() => {
    const section = sectionRef.current
    const container = containerRef.current

    if (!section || !container) return

    // Set initial state
    gsap.set(container, {
      maxWidth: '64rem', // max-w-4xl equivalent
      borderRadius: '1rem', // rounded-2xl equivalent
      boxShadow:
        '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
    })

    // Create the futuristic expansion animation
    const scrollTrigger = ScrollTrigger.create({
      trigger: section,
      start: 'top 80%', // Start animation when section enters viewport
      end: 'bottom 20%', // End animation when section is about to leave
      scrub: 2, // Smooth scrubbing with slight delay for dramatic effect
      onUpdate: (self) => {
        // Only animate if not in fullscreen mode
        if (!isFullscreen) {
          const progress = self.progress

          // Calculate dynamic values based on scroll progress
          const maxWidth = gsap.utils.interpolate('64rem', '100vw', progress)
          const borderRadius = gsap.utils.interpolate(16, 0, progress)
          const scale = gsap.utils.interpolate(1, 1.02, progress)
          const shadowIntensity = gsap.utils.interpolate(0.1, 0.3, progress)
          const glowIntensity = progress * 0.5

          // Apply futuristic transformation
          gsap.to(container, {
            maxWidth: maxWidth,
            borderRadius: `${borderRadius}px`,
            scale: scale,
            boxShadow: `
              0 ${20 + progress * 30}px ${
              50 + progress * 50
            }px -3px rgba(0, 0, 0, ${shadowIntensity}),
              0 ${8 + progress * 12}px ${
              25 + progress * 25
            }px -2px rgba(0, 0, 0, ${shadowIntensity * 0.5}),
              0 0 ${progress * 100}px rgba(59, 130, 246, ${glowIntensity}),
              0 0 ${progress * 200}px rgba(147, 51, 234, ${glowIntensity * 0.5})
            `,
            duration: 0.1,
            ease: 'none',
            transformOrigin: 'center center'
          })

          // Add futuristic glow effect to the video itself
          const video = container.querySelector('video')
          if (video) {
            gsap.to(video, {
              filter: `
                brightness(${1 + progress * 0.1})
                contrast(${1 + progress * 0.1})
                saturate(${1 + progress * 0.2})
                hue-rotate(${progress * 10}deg)
              `,
              duration: 0.1,
              ease: 'none'
            })
          }
        }
      },
      onLeave: () => {
        // Keep the expanded state when scrolling past the section
        if (!isFullscreen) {
          gsap.set(container, {
            maxWidth: '100vw',
            borderRadius: '0px',
            scale: 1.02,
            boxShadow: `
              0 50px 100px -3px rgba(0, 0, 0, 0.3),
              0 20px 50px -2px rgba(0, 0, 0, 0.15),
              0 0 100px rgba(59, 130, 246, 0.5),
              0 0 200px rgba(147, 51, 234, 0.25)
            `
          })
        }
      }
    })

    // Cleanup function
    return () => {
      scrollTrigger.kill()
    }
  }, [isFullscreen]) // Re-run when fullscreen state changes

  const handleVideoToggle = () => {
    if (videoRef.current) {
      if (isVideoPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
        videoRef.current.muted = false
      }
      setIsVideoPlaying(!isVideoPlaying)
    }
  }

  const enterFullscreen = async () => {
    if (containerRef.current && !isFullscreen) {
      try {
        await containerRef.current.requestFullscreen()
      } catch (error) {
        console.error('Error entering fullscreen:', error)
      }
    }
  }

  const exitFullscreen = async () => {
    if (isFullscreen) {
      try {
        await document.exitFullscreen()
      } catch (error) {
        console.error('Error exiting fullscreen:', error)
      }
    }
  }

  const handleDoubleClick = () => {
    if (isFullscreen) {
      exitFullscreen()
    } else {
      enterFullscreen()
    }
  }

  const handleVideoClick = (event: React.MouseEvent) => {
    // Prevent the overlay click when clicking on video controls
    if ((event.target as HTMLElement).closest('video')) {
      return
    }
    handleVideoToggle()
  }

  return (
    <section ref={sectionRef} className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-5xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-normal text-gray-900 mb-4">
            See How It Works
          </h2>
          <p className="text-lg text-gray-600">
            Watch a demonstration of Ulo Business Profile in action
          </p>
        </div>

        <div
          ref={containerRef}
          className={`relative bg-gray-900 overflow-hidden aspect-video mx-auto transition-all duration-300 ${
            isFullscreen ? 'fixed inset-0 z-50 rounded-none max-w-none' : ''
          }`}
          onDoubleClick={handleDoubleClick}
        >
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            autoPlay
            muted
            loop
            controls
            controlsList="nodownload"
            onPlay={() => setIsVideoPlaying(true)}
            onPause={() => setIsVideoPlaying(false)}
            onDoubleClick={handleDoubleClick}
            poster="/video-placeholder.jpg"
          >
            <source src={heroContent.videoUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>

          {/* Gradient overlay - only show when not in fullscreen */}
          {!isFullscreen && (
            <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-purple-600/20 pointer-events-none"></div>
          )}

          {/* Custom play/pause overlay - only show when video is paused and not in fullscreen */}
          {!isVideoPlaying && !isFullscreen && (
            <div
              className="absolute inset-0 flex items-center justify-center cursor-pointer bg-black bg-opacity-30"
              onClick={handleVideoClick}
            >
              <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-105">
                <Play
                  className="w-8 h-8 text-gray-900 ml-1 group-hover:scale-110 transition-transform"
                  fill="currentColor"
                />
              </div>
            </div>
          )}

          {/* Video description - only show when not in fullscreen */}
          {!isFullscreen ||
            (isVideoPlaying && (
              <div className="absolute bottom-4 left-4 text-white pointer-events-none">
                <p className="text-sm opacity-80">
                  See how Ulo Business Profile works
                </p>
              </div>
            ))}

          {/* Fullscreen instructions - only show in fullscreen */}
          {isFullscreen && (
            <div className="absolute top-4 right-4 text-white bg-black bg-opacity-50 px-3 py-2 rounded text-sm pointer-events-none">
              Press ESC or double-click to exit fullscreen
            </div>
          )}
        </div>
      </div>
    </section>
  )
}

export default VideoShowcaseSection