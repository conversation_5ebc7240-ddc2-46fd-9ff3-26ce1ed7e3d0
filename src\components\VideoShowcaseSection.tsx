import React, { useRef, useState, useEffect } from 'react'
import { Play } from 'lucide-react'
import { HeroContent } from '@/types'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

interface VideoShowcaseSectionProps {
  heroContent: HeroContent
}

const VideoShowcaseSection: React.FC<VideoShowcaseSectionProps> = ({
  heroContent
}) => {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const sectionRef = useRef<HTMLElement>(null)

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () =>
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  // Handle escape key for fullscreen exit
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        exitFullscreen()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isFullscreen])

  // Sync video playing state with actual video state
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handlePlay = () => setIsVideoPlaying(true)
    const handlePause = () => setIsVideoPlaying(false)

    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)

    return () => {
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
    }
  }, [])

  // GSAP ScrollTrigger animation for futuristic bidirectional video expansion
  useEffect(() => {
    const section = sectionRef.current
    const container = containerRef.current

    if (!section || !container) return

    // Set initial state - start with small width (400px) and centered
    gsap.set(container, {
      width: '400px',
      maxWidth: '400px',
      borderRadius: '16px',
      y: 0,
      boxShadow:
        '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      transformOrigin: 'center center'
    })

    // Create the futuristic bidirectional expansion animation
    const scrollTrigger = ScrollTrigger.create({
      trigger: section,
      start: 'top 70%', // Start when section enters viewport
      end: 'bottom 30%', // End when section is about to leave
      scrub: 1, // Fast, responsive scrubbing like header animation
      onUpdate: (self) => {
        // Only animate if not in fullscreen mode
        if (!isFullscreen) {
          const progress = self.progress

          // Calculate responsive final width based on screen size
          const getMaxWidth = () => {
            const sectionWidth = section.getBoundingClientRect().width
            const containerPadding = 64 // Account for section padding
            return Math.min(sectionWidth - containerPadding, 1200) // Max 1200px, but responsive
          }

          // Calculate dynamic values based on scroll progress
          const finalWidth = getMaxWidth()
          const currentWidth = gsap.utils.interpolate(400, finalWidth, progress)
          const borderRadius = gsap.utils.interpolate(16, 8, progress)
          const yOffset = gsap.utils.interpolate(0, 20, progress) // Subtle downward slide
          const shadowIntensity = gsap.utils.interpolate(0.1, 0.25, progress)
          const glowIntensity = progress * 0.4

          // Apply fast, futuristic transformation (similar to header animation style)
          gsap.to(container, {
            width: `${currentWidth}px`,
            maxWidth: `${currentWidth}px`,
            borderRadius: `${borderRadius}px`,
            y: yOffset,
            boxShadow: `
              0 ${15 + progress * 25}px ${
              35 + progress * 35
            }px -3px rgba(0, 0, 0, ${shadowIntensity}),
              0 ${6 + progress * 10}px ${
              20 + progress * 20
            }px -2px rgba(0, 0, 0, ${shadowIntensity * 0.6}),
              0 0 ${progress * 80}px rgba(59, 130, 246, ${glowIntensity}),
              0 0 ${progress * 160}px rgba(147, 51, 234, ${glowIntensity * 0.6})
            `,
            duration: 0.3, // Fast animation like header
            ease: 'power2.out', // Same easing as header
            transformOrigin: 'center center'
          })

          // Add futuristic video effects
          const video = container.querySelector('video')
          if (video) {
            gsap.to(video, {
              filter: `
                brightness(${1 + progress * 0.08})
                contrast(${1 + progress * 0.08})
                saturate(${1 + progress * 0.15})
                hue-rotate(${progress * 8}deg)
              `,
              duration: 0.3,
              ease: 'power2.out'
            })
          }
        }
      },
      onLeave: () => {
        // Keep the expanded state when scrolling past the section
        if (!isFullscreen) {
          const finalWidth = Math.min(
            section.getBoundingClientRect().width - 64,
            1200
          )
          gsap.set(container, {
            width: `${finalWidth}px`,
            maxWidth: `${finalWidth}px`,
            borderRadius: '8px',
            y: 20,
            boxShadow: `
              0 40px 60px -3px rgba(0, 0, 0, 0.25),
              0 16px 40px -2px rgba(0, 0, 0, 0.15),
              0 0 80px rgba(59, 130, 246, 0.4),
              0 0 160px rgba(147, 51, 234, 0.24)
            `
          })

          // Keep video effects at final state
          const video = container.querySelector('video')
          if (video) {
            gsap.set(video, {
              filter: `
                brightness(1.08)
                contrast(1.08)
                saturate(1.15)
                hue-rotate(8deg)
              `
            })
          }
        }
      },
      onLeaveBack: () => {
        // Reset to initial state when scrolling back up past the trigger
        if (!isFullscreen) {
          gsap.to(container, {
            width: '400px',
            maxWidth: '400px',
            borderRadius: '16px',
            y: 0,
            boxShadow:
              '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
            duration: 0.6,
            ease: 'power2.out'
          })

          const video = container.querySelector('video')
          if (video) {
            gsap.to(video, {
              filter: 'brightness(1) contrast(1) saturate(1) hue-rotate(0deg)',
              duration: 0.6,
              ease: 'power2.out'
            })
          }
        }
      }
    })

    // Handle window resize for responsive behavior
    const handleResize = () => {
      if (scrollTrigger) {
        scrollTrigger.refresh()
      }
    }

    window.addEventListener('resize', handleResize)

    // Cleanup function
    return () => {
      scrollTrigger.kill()
      window.removeEventListener('resize', handleResize)
    }
  }, [isFullscreen]) // Re-run when fullscreen state changes

  const handleVideoToggle = () => {
    if (videoRef.current) {
      if (isVideoPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
        videoRef.current.muted = false
      }
      setIsVideoPlaying(!isVideoPlaying)
    }
  }

  const enterFullscreen = async () => {
    if (containerRef.current && !isFullscreen) {
      try {
        await containerRef.current.requestFullscreen()
      } catch (error) {
        console.error('Error entering fullscreen:', error)
      }
    }
  }

  const exitFullscreen = async () => {
    if (isFullscreen) {
      try {
        await document.exitFullscreen()
      } catch (error) {
        console.error('Error exiting fullscreen:', error)
      }
    }
  }

  const handleDoubleClick = () => {
    if (isFullscreen) {
      exitFullscreen()
    } else {
      enterFullscreen()
    }
  }

  const handleVideoClick = (event: React.MouseEvent) => {
    // Prevent the overlay click when clicking on video controls
    if ((event.target as HTMLElement).closest('video')) {
      return
    }
    handleVideoToggle()
  }

  return (
    <section ref={sectionRef} className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-5xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-normal text-gray-900 mb-4">
            See How It Works
          </h2>
          <p className="text-lg text-gray-600">
            Watch a demonstration of Ulo Business Profile in action
          </p>
        </div>

        <div
          ref={containerRef}
          className={`relative bg-gray-900 overflow-hidden aspect-video mx-auto transition-all duration-300 ${
            isFullscreen ? 'fixed inset-0 z-50 rounded-none max-w-none' : ''
          }`}
          onDoubleClick={handleDoubleClick}
        >
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            autoPlay
            muted
            loop
            controls
            controlsList="nodownload"
            onPlay={() => setIsVideoPlaying(true)}
            onPause={() => setIsVideoPlaying(false)}
            onDoubleClick={handleDoubleClick}
            poster="/video-placeholder.jpg"
          >
            <source src={heroContent.videoUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>

          {/* Gradient overlay - only show when not in fullscreen */}
          {!isFullscreen && (
            <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-purple-600/20 pointer-events-none"></div>
          )}

          {/* Custom play/pause overlay - only show when video is paused and not in fullscreen */}
          {!isVideoPlaying && !isFullscreen && (
            <div
              className="absolute inset-0 flex items-center justify-center cursor-pointer bg-black bg-opacity-30"
              onClick={handleVideoClick}
            >
              <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-105">
                <Play
                  className="w-8 h-8 text-gray-900 ml-1 group-hover:scale-110 transition-transform"
                  fill="currentColor"
                />
              </div>
            </div>
          )}

          {/* Video description - only show when not in fullscreen */}
          {!isFullscreen ||
            (isVideoPlaying && (
              <div className="absolute bottom-4 left-4 text-white pointer-events-none">
                <p className="text-sm opacity-80">
                  See how Ulo Business Profile works
                </p>
              </div>
            ))}

          {/* Fullscreen instructions - only show in fullscreen */}
          {isFullscreen && (
            <div className="absolute top-4 right-4 text-white bg-black bg-opacity-50 px-3 py-2 rounded text-sm pointer-events-none">
              Press ESC or double-click to exit fullscreen
            </div>
          )}
        </div>
      </div>
    </section>
  )
}

export default VideoShowcaseSection